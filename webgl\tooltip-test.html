<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tooltip 修复测试</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background: #0a0f1c;
            color: #ffffff;
            font-family: 'Microsoft YaHei', sans-serif;
            padding: 20px;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 8px;
        }
        
        .test-title {
            color: #00d4ff;
            margin-bottom: 20px;
            font-size: 18px;
        }
        
        .parameter-grid-test {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        
        .tooltip-test-btn {
            padding: 10px 15px;
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 6px;
            color: #ffffff;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Tooltip 修复测试页面</h1>
        
        <div class="test-section">
            <h2 class="test-title">1. Parameter-item 专用 Tooltip（修复后）</h2>
            <div class="parameter-grid-test">
                <div class="parameter-item" onclick="showParameterDetails('test1')">
                    <div class="parameter-content">
                        <div class="parameter-label">负载无功功率:</div>
                        <div class="parameter-value" id="test-value-1">-2.35 MVAr</div>
                    </div>
                </div>
                
                <div class="parameter-item" onclick="showParameterDetails('test2')">
                    <div class="parameter-content">
                        <div class="parameter-label">功率因数:</div>
                        <div class="parameter-value" id="test-value-2">0.95</div>
                    </div>
                </div>
                
                <div class="parameter-item" onclick="showParameterDetails('test3')">
                    <div class="parameter-content">
                        <div class="parameter-label">母线电压Uab:</div>
                        <div class="parameter-value" id="test-value-3">10.52 kV</div>
                    </div>
                </div>
                
                <div class="parameter-item" onclick="showParameterDetails('test4')">
                    <div class="parameter-content">
                        <div class="parameter-label">SVG电流Ia:</div>
                        <div class="parameter-value" id="test-value-4">128.5 A</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">2. 通用 Tooltip（使用 tooltip-enabled 类）</h2>
            <button class="tooltip-test-btn tooltip-enabled" title="这是一个通用的 tooltip 示例">
                悬停查看通用 Tooltip
            </button>
            <button class="tooltip-test-btn tooltip-enabled" title="这是另一个通用 tooltip，内容稍长一些，用于测试换行和边界处理">
                悬停查看长文本 Tooltip
            </button>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">3. 测试说明</h2>
            <ul>
                <li>Parameter-item 元素应该只显示一个自定义 tooltip</li>
                <li>Tooltip 应该不被容器边框切割</li>
                <li>Tooltip 应该在鼠标悬停时正确定位</li>
                <li>通用元素的 tooltip 应该正常工作</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟 parameter-item 的 tooltip 更新
        function updateTestTooltips() {
            const testItems = document.querySelectorAll('.parameter-item');
            testItems.forEach((item, index) => {
                const valueElement = item.querySelector('.parameter-value');
                const labelElement = item.querySelector('.parameter-label');
                
                if (valueElement && labelElement) {
                    const tooltipText = `${labelElement.textContent} ${valueElement.textContent} (更新时间: ${new Date().toLocaleTimeString()})`;
                    updateParameterTooltip(valueElement, tooltipText);
                }
            });
        }

        // 复制主页面的 updateParameterTooltip 函数
        function updateParameterTooltip(element, tooltipText) {
            const parameterItem = element.closest('.parameter-item') || 
                               (element.classList.contains('parameter-item') ? element : null);
          
            if (!parameterItem) {
                element.title = tooltipText;
                return;
            }

            let tooltip = parameterItem.querySelector('.parameter-tooltip');
            if (!tooltip) {
                tooltip = document.createElement('div');
                tooltip.className = 'parameter-tooltip';
                parameterItem.appendChild(tooltip);

                parameterItem.addEventListener('mouseenter', (e) => {
                    const rect = parameterItem.getBoundingClientRect();
                    const tooltipRect = tooltip.getBoundingClientRect();
                    
                    let left = rect.left + rect.width / 2 - tooltipRect.width / 2;
                    let top = rect.top - tooltipRect.height - 10;
                    
                    if (left < 10) left = 10;
                    if (left + tooltipRect.width > window.innerWidth - 10) {
                        left = window.innerWidth - tooltipRect.width - 10;
                    }
                    if (top < 10) {
                        top = rect.bottom + 10;
                    }
                    
                    tooltip.style.left = left + 'px';
                    tooltip.style.top = top + 'px';
                });
            }

            tooltip.textContent = tooltipText;
        }

        function showParameterDetails(param) {
            console.log('显示参数详情:', param);
        }

        // 页面加载后初始化 tooltips
        document.addEventListener('DOMContentLoaded', function() {
            updateTestTooltips();
            console.log('Tooltip 测试页面已加载');
        });
    </script>
</body>
</html>
