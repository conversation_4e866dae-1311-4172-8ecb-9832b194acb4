/**
 * 桂林智源 SVG 数字化系统 - 配置文件
 * 统一管理API认证token和其他全局配置
 */

// 统一的JWT Token配置
const CONFIG = {
    // JWT认证Token
    AUTH_TOKEN: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjVjODI5MjEzLTZlMGEtNDI5Mi1hY2QzLTQ1YWY1YjUxN2YwOCJ9.7bK4P4xjtL2w6U0A9ewqCFyh2KVrTOuUDzYvlNesTL3uh0OnDRyNhs1oV4B08tZB_wUYWOmkatgf_wcJo1VZ8A',
    
    // API基础URL
    BASE_URL: 'https://exdraw.qizhiyun.cc/prod-api',
    
    // 请求超时时间（毫秒）
    REQUEST_TIMEOUT: 10000,
    
    // 重试次数
    MAX_RETRIES: 3
};

/**
 * 获取认证头信息
 * @returns {Object} 包含Authorization头的对象
 */
function getAuthHeader() {
    return {
        'Authorization': `Bearer ${CONFIG.AUTH_TOKEN}`
    };
}

/**
 * 获取完整的请求配置
 * @param {Object} additionalHeaders 额外的请求头
 * @returns {Object} 完整的请求配置
 */
function getRequestConfig(additionalHeaders = {}) {
    return {
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            ...getAuthHeader(),
            ...additionalHeaders
        },
        timeout: CONFIG.REQUEST_TIMEOUT
    };
}

/**
 * 更新认证Token
 * @param {string} newToken 新的JWT Token
 */
function updateAuthToken(newToken) {
    CONFIG.AUTH_TOKEN = newToken;
    console.log('认证Token已更新');
}

/**
 * 获取当前Token（调试用）
 * @returns {string} 当前Token
 */
function getCurrentToken() {
    return CONFIG.AUTH_TOKEN;
}

// 全局导出，供其他文件使用
if (typeof window !== 'undefined') {
    window.CONFIG = CONFIG;
    window.getAuthHeader = getAuthHeader;
    window.getRequestConfig = getRequestConfig;
    window.updateAuthToken = updateAuthToken;
    window.getCurrentToken = getCurrentToken;
}

// ES6模块导出（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CONFIG,
        getAuthHeader,
        getRequestConfig,
        updateAuthToken,
        getCurrentToken
    };
}